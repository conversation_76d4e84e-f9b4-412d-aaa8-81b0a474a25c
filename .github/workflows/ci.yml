name: Continuous integration

# Runs on push and pull requests, any branch, Linux only (for now).

on: [push, pull_request]

jobs:
  # Linux ----------------------------------------------------------------------
  linux:
    name: Linux CI
    runs-on: ubuntu-24.04
    timeout-minutes: 60
    steps:
      - name: Download repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install dependencies
        run: bash ./.github/scripts/linux/install-deps.sh
        
      - name: Generate Makefile
        run: cmake -S . -B build/ -G "Unix Makefiles" -DCMAKE_TOOLCHAIN_FILE=$VCPKG_INSTALLATION_ROOT/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Debug -DWITH_TESTS=ON -DWITH_VST3=ON

      - name: Build
        run: cmake --build build/ -j 3

      - name: Run tests
        run: xvfb-run ./build/giada --run-tests 

  # Windows --------------------------------------------------------------------
  windows:
    name: Windows
    runs-on: windows-2022
    timeout-minutes: 60
    steps:
      - name: Download repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Generate Makefile
        shell: bash
        run: cmake -S . -B build/ -G "Visual Studio 17 2022" -DCMAKE_TOOLCHAIN_FILE=$VCPKG_INSTALLATION_ROOT/scripts/buildsystems/vcpkg.cmake -DWITH_TESTS=ON -DWITH_VST3=ON

      - name: Build
        shell: bash
        run: cmake --build build/ --config Debug -j 3

      - name: Run tests
        run: ./build/Debug/giada.exe --run-tests 

  # macOS ----------------------------------------------------------------------
  macos:
    name: macOS
    runs-on: macOS-13
    timeout-minutes: 60
    steps:
      - name: Download repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Generate Makefile
        run: cmake -S . -B build/ -G "Xcode" -DCMAKE_TOOLCHAIN_FILE=$VCPKG_INSTALLATION_ROOT/scripts/buildsystems/vcpkg.cmake -DCMAKE_CXX_FLAGS="-x objective-c++" -DWITH_TESTS=ON -DWITH_VST3=ON

      - name: Build
        run: cmake --build build/ --config Debug -j 2

      - name: Run tests
        run: ./build/Debug/giada --run-tests 