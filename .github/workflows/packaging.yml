name: Packaging

# Runs on new tags and manually (workflow_dispatch).

on:
  workflow_dispatch:
  push:
    tags:
    - '*'

jobs:
  # create-release -------------------------------------------------------------
  create-release:
    name: Create release
    runs-on: ubuntu-24.04
    outputs: # Output contains the upload url. Will be reused on upload release
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Create
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: true
          prerelease: false

  # Linux ----------------------------------------------------------------------
  linux:
    name: Linux
    runs-on: ubuntu-24.04
    timeout-minutes: 60
    needs: create-release
    steps:
      - name: Download repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install dependencies
        run: ./.github/scripts/linux/install-deps.sh
        shell: bash
        
      - name: Generate Makefile
        run: cmake -S . -B build/ -G "Unix Makefiles" -DCMAKE_TOOLCHAIN_FILE=$VCPKG_INSTALLATION_ROOT/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS=-Wno-class-memaccess -DWITH_VST3=ON

      - name: Build
        run: cmake --build build/ -j 2

      - name: Make package
        run: ./.github/scripts/linux/make-package.sh ${{ github.ref_name }}
        shell: bash

      - name: Upload release
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: dist/Giada-${{ github.ref_name }}-x86_64.AppImage
          asset_name: Giada-${{ github.ref_name }}-x86_64.AppImage
          asset_content_type: application/x-executable

  # Windows --------------------------------------------------------------------
  windows:
    name: Windows
    runs-on: windows-2022
    timeout-minutes: 60
    needs: create-release
    steps:
      - name: Download repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
        
      - name: Generate Makefile
        shell: bash
        run: cmake -S . -B build/ -G "Visual Studio 17 2022" -DCMAKE_TOOLCHAIN_FILE=$VCPKG_INSTALLATION_ROOT/scripts/buildsystems/vcpkg.cmake -DVCPKG_TARGET_TRIPLET=x64-windows-static-md -DWITH_VST3=ON

      - name: Build
        shell: bash
        run: cmake --build build/ --config Release -j 3

      - name: Make package
        run: bash ./.github/scripts/windows/make-package.sh ${{ github.ref_name }}

      - name: Upload release
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: dist/giada-${{ github.ref_name }}-x86_64-windows.zip
          asset_name: giada-${{ github.ref_name }}-x86_64-windows.zip
          asset_content_type: application/zip

  # macOS ----------------------------------------------------------------------
  macos:
    name: macOS
    runs-on: macOS-13
    timeout-minutes: 60
    needs: create-release
    steps:
      - name: Download repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Generate Makefile
        run: cmake -S . -B build/ -G "Xcode" -DCMAKE_TOOLCHAIN_FILE=$VCPKG_INSTALLATION_ROOT/scripts/buildsystems/vcpkg.cmake -DCMAKE_CXX_FLAGS="-x objective-c++" -DWITH_VST3=ON

      - name: Build
        run: cmake --build build/ --config Release -j 3

      - name: Make package
        run: bash ./.github/scripts/macos/make-package.sh ${{ github.ref_name }}

      - name: Upload release
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: dist/giada-${{ github.ref_name }}-x86_64-macos.zip
          asset_name: giada-${{ github.ref_name }}-x86_64-macos.zip
          asset_content_type: application/zip

  # source ---------------------------------------------------------------------
  source:
    name: Source
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    needs: create-release
    if: github.ref_type == 'tag' # This job needs a tag for the make-src.py script
    steps:
      - name: Download repository
        uses: actions/checkout@v4

      - name: Make source package
        run: python3 ./.github/scripts/make-src.py ${{ github.ref_name }}

      - name: Upload release
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: dist/giada-${{ github.ref_name }}-src.tar.gz
          asset_name: giada-${{ github.ref_name }}-src.tar.gz
          asset_content_type: application/gzip
