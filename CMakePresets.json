{"version": 6, "configurePresets": [{"name": "common", "hidden": true, "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": {"value": "$env{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake", "type": "FILEPATH"}, "WITH_VST3": "ON"}}, {"name": "common-linux", "inherits": "common", "hidden": true, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Linux"}}, {"name": "common-windows", "inherits": "common", "hidden": true, "generator": "Visual Studio 17 2022", "architecture": "x64", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "common-macos", "inherits": "common", "hidden": true, "generator": "Xcode", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "<PERSON>"}}, {"name": "debug-linux", "inherits": "common-linux", "displayName": "Debug configuration for Linux", "binaryDir": "${sourceDir}/build/debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "WITH_TESTS": "ON"}}, {"name": "release-linux", "inherits": "common-linux", "binaryDir": "${sourceDir}/build/release", "displayName": "Release configuration for Linux", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "debug-windows", "inherits": "common-windows", "displayName": "Debug configuration for Windows", "binaryDir": "${sourceDir}/build/debug", "cacheVariables": {"WITH_TESTS": "ON"}}, {"name": "release-windows", "inherits": "common-windows", "displayName": "Release configuration for Windows", "binaryDir": "${sourceDir}/build/release"}, {"name": "debug-macos", "inherits": "common-macos", "displayName": "Debug configuration for macOS", "binaryDir": "${sourceDir}/build/debug", "cacheVariables": {"WITH_TESTS": "ON"}}, {"name": "release-macos", "inherits": "common-macos", "displayName": "Release configuration for macOS", "binaryDir": "${sourceDir}/build/release"}]}