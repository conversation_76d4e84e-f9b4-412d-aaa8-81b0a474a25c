--------------------------------------------------------------------------------


  Giada - Your Hardcore Loopmachine.

  Developed by Monocasual Laboratories

  www.giadamusic.com

  CHANGELOG
--------------------------------------------------------------------------------


1.2.0 --- 2025 . 04 . 22
- Add support for multiple MIDI devices (#215)
- Preserve position and size of 'Settings' window
- Fix wrong name in cloned sample channels (#725)
- Update concurrentqueue dependency to version 1.0.4
- Update FLTK dependency to 1.4.2
- More CMake presets improvements
- Refactoring and code gardening


1.1.1 --- 2025 . 01 . 07
- Update FLTK dependency to 1.4.0-1
- <PERSON>der Group Channels audio data to master output only if audible (#709)
- Fix MIDI out information not being refreshed properly on MIDI channels (#717)
- Fix MIDI actions not being added on the rightmost edge in the Action Editor (#716)
- Fix FX buttons not being properly lit (#712)
- Fix ChannelShared objects not being deleted on Channel deletion (#695)
- Fix wrong play mode for sample channels in one-shot retrig mode
- Fix missing last sample when moving or resetting 'end' point in Sample Editor
- Fix last audio sample not being played in sample channels
- Fix inability to clear Channels' key bindings (#708)
- Fix Main Window size not being stored correctly (#713)
- Add basic CMakePresets.json file
- Many other minor bugs and typos fixed
- Lots of refactoring and code cleanups


1.1.0 --- 2024 . 10 . 25
- New "Channel Groups" feature
- Update FLTK library to latest master
- Update JUCE Framework to 8.0.1
- Lots of refactoring and code cleanups


1.0.0 --- 2024 . 02 . 24
- Main Menu redesign with macOS support
- Main Window I/O redesign with vertical audio meters
- Show beat number in Main Sequencer
- Redesigned Column menu with improved usability
- New Velocity Editor widget in Sample Channel Action Editor
- Recursive buffer rendering implementation (#538)
- Let Giada pick a default audio device if not specified (#669)
- Send MIDI data through armed channels to the outside world (#498)
- [Linux] Remove duplicated .desktop file generation (#591)
- [Linux] Update metainfo.xml file
- Optimize audio preview rendering: enable it only when Sample Editor is open
- Improved zoom with mouse wheel in Sample Editor and Action Editor
- Fix some Sample Editor operations not working correctly
- Fix grid not showing up correctly in Sample Editor (#679)
- Fix inability to quit record-on-signal mode (#677)
- Fix Record-on-signal mode not deactivated after recording audio (#678)
- Refactoring and code cleanup


0.26.1 --- 2023 . 12. 19
- Add ability to stop plug-ins scan (#423)
- Add ability to sort plug-ins in both ascending and descending order (#542)
- Disable main transport buttons when not available, with better explanation (#629)
- [Linux] Follow the XDG Base Directory Specification for configuration files (#338)
- Close all subwindows on project close before resetting the engine
- Fix assertion when when applying audio config changes and input device is disabled
- Fix assertion when selected RtMidi API is not present in available APIs (#670)
- Fix Sample Editor not picking up Channel properties (pitch, range, ...) when loading a project
- Fix crash when fetching audio device and JACK is in use
- Fix some scrolling glitches in Action Editor
- [Windows] Fix plug-in GUI weird auto-resize when dragged around (#621)
- Refactoring and code cleanup


0.26.0 --- 2023 . 10. 15
- Add ability to open more than one plug-in UI (#575)
- Huge optimizations to the audio rendering process 
- Improved floating legends in Action Editor (#655)
- Enabling MIDI lightning also enables MIDI input (#527)
- Enable static linking in Windows build (#661)
- Sample Editor: play preview with the right current pitch and range (#654)
- Right-click over empty column space opens the column menu (#628)
- Don't pass command line arguments to FLTK (#372)
- [Windows] Store configuration file in AppData directory (#299)
- Update FLTK to latest master
- Update RtAudio to 6.0.1
- Lots of code refactoring and internal cleanup


0.25.1 --- 2023 . 08 . 03
- Enabled realtime scheduling in audio callback (#68)
- Input audio meter is now affected by the current input volume value (#239)
- Improved file name validation when saving projects and samples (#641)
- Fix compilation with format-security compiler flags (#447)
- Disable metronome when recording in free-loop-length mode (#514)
- Improved robustness of audio channel selection when changing audio devices in
  the configuration panel
- Upgrade libsndfile library to version 1.2.0
- Upgrade nlohmann-json library to version 3.11.2
- Upgrade fmt library to version 10.0.0
- Lots of code refactoring and internal cleanup


0.25.0 --- 2023 . 06 . 05
- Ability to change audio and MIDI engines on the fly — no need to restart Giada 
  anymore when the audio and/or MIDI devices change. No need to restart also
  when changing several properties in the Configuration Window;
- Add new "Clear" button next to all MIDI learning widgets to easily clear the
  MIDI binding (#486);
- Set default resampler quality to "Linear" (the fastest one);
- Update FLTK to latest commit available;
- Update JUCE to version 7.0.5;
- Fix Velocity parameter not being usable in Action Editor;
- Fix MIDI Channel parameter not being usable in MIDI input configuration;
- Code refactoring and other architectural improvements.


0.24.0 --- 2023 . 03 . 15
- Revamped MIDI sync Clock support
- MIDI engine refactoring, featuring multi-threading support
- UI: Resizable vu-meters
- UI: New global MIDI activity leds  
- More core classes and functions refactoring
- More robust persistence mechanism for project and configuration files
- Add new tests suites for MidiEvent and ChannelFactory
- Set C++ version to C++20
- Update FLTK to latest commit available


0.23.2 --- 2022 . 12 . 05
- 'Kill channel' action now rewinds channels in SINGLE_BASIC_PAUSE mode (#599)
- Update FLTK to latest commit available
- Fix audio recording always getting trimmed to 4 beats (#613)
- Fix weird noise when playing two cloned Sample Channels with altered pitch (#602)
- Fix MIDI channel behavior when key is pressed and channel has ENDING state
- Fix channel's keyboard triggering not working
- Fix missing begin/end points and shift value when cloning Sample channels (#601)


0.23.0 --- 2022 . 09 . 18
- Moveable channels between columns
- Initial LV2 plug-ins support, thanks to JUCE v7.0.1
- Add high DPI screens support + SVG icons, thanks to FLTK 1.4.0
- Store Plug-in List Window width and height in configuration file
- Set JSON for Modern C++ as an external dependency
- Fix Sample Channels that couldn't be killed while in ENDING status
- Lots of code refactoring in Channel class and related components
- Code clean-ups for many UI widgets


0.22.0 --- 2022 . 06 .  13
- Multi-language support via langmaps (#34)
- Add new 'fmt' dependency
- Add vcpkg.json manifest file
- Log compiled RtMidi APIs on startup
- Fix missing header with RtMidi 5.0.0
- Fix Stream Linker button not working (#585)
- Minor code refactoring and cleanups


0.21.0 --- 2022 . 04 . 01
- Custom keyboard mappings for global actions (#213)
- Pressing 'Esc' key no longer closes windows
- Resizable Configuration window
- Shut down the main UI nicely when closing a project or loading a new one
- Prevent crashes when loading a new project by cleaning up the data model 
- Fix wrong plug-in processing where the local plug-in buffer was incorrectly 
  deleted in case of instruments (#563)
- Lots of UI code modernizations and cleanups
- [Windows] Fix several assertions and MSVC warnings


0.20.1 --- 2022 . 02 . 21
- New MIDI I/O activity LEDs on channels (#143)
- New "Missing Assets" alert window (#344)
- Many smaller improvements and cleanups in UI code
- Add ability to sort installed plug-ins by Format (VST, VST3, ...)
- Update JUCE to 6.1.5
- Update custom RtAudio submodule (now pointing to 6.0.0beta1)
- Optimize internal buffer Giada <-> JUCE conversion
- Remove old plug-in parameter storage used in old patches
- Fix deadlock when using JACK transport
- Fix Action Editor grid refresh when changing BPM while the editor window is open (#547)
- Fix plug-in clone operation while cloning a channel (#551)


0.20.0 --- 2022 . 01 . 24
- Show progress bar for long operations 
- Improved rendering algorithm for sample channels
- Fix wrong sample tail rendering when pitch != 1.0 
- Always display play head in Action Editor (fix #534)
- Fix re-initialization order of engine sub-components (fixes #533)
- Change 'kill chan' wording to 'stop note' in Action Editor (fixes #532)
- Update solo count when deleting a channel (fixes #540)
- Update Main Window title saving a new project (fixes #541)
- [Config] Don't skip MIDI device fetching if one of the ports fail to open
- [CMake] Include FLTK as suggested in the official docs
- Add more unit tests for some Channel components
- Minor cleanups and refactoring


0.19.2 --- 2021 . 12 . 16
- Fix wrong computation of soloed channels


0.19.1 --- 2021 . 12 . 15
- Enable JUCE_DEBUG in Debug builds
- New MidiLighter tests + compile-time dependency injection
- Set limits to minimum zoom level in Action Editors (#425)
- Refactoring and code cleanup for Channel class and other sub-components 
- Update JUCE to version 6.1.2
- Update RtAudio to version 5.2.0
- Sanitize MIDI ports values (fixes #515)
- MidiLighter improvements and cleanups (fixes #517)
- Fix off-the-beat metronome (#522)
- Fix number of plug-ins found not being updated after a scan (fix #523)
- Fix PluginManager initialization
- Fix pthread linking in CMake (#520)
- Fix build info not being printed correctly on startup
- [Linux] Fix X error messages on closing some plug-in editors
- [Linux] Fix wrong icon file in XDG desktop file


0.19.0 --- 2021 . 11 . 01
- New "One-shot Pause" channel mode
- Refactoring: new component-based architecture
- Fix crash on startup if recording from mono input
- Improved event handling for plug-ins GUIs 
- Fix many compiler warnings on menu items initialization


0.18.2 --- 2021 . 09 . 13
- New stereo In/Out audio meters 
- Revamped Action Editor: better UI, improved usability 
- Show play head in Action Editor
- Implement queue for MIDI events, fix issue #482
- Simplified Event Dispatcher's Event type
- Move JACK transport operations to new JackTransport class
- Always pick sample rate from the first audio device when using JACK
- Don't send MIDI events if MIDI channel is not playing (#499) or muted (#497)
- Add AtomicSwapper as git submodule
- Upgrade JUCE to version 6.1.0


0.18.1 --- 2021 . 07 . 25
- New resampler architecture: allows for changing quality also for live rendering (#288)
- Gracefully shutdown UI on close to random crashes on quit on Windows 
- Fix 'one shot channels with actions as loops' mode not working correctly
- Fix wrong sequencer signals while starting/stopping action recs with JACK (#397)
- Fix extra dot in unique audio file name generation
- Fix sample overflow when looping a sample with pitch != 1.0
- [CMake, Linux] Detect JACK with pkg-config
- [CMake, Linux] Install Freedesktop files and icon
- [CMake, Linux] Add configure switches for ALSA, JACK and PulseAudio
- [macOS] Enable hardened runtime


0.18.0 --- 2021 . 05 . 18
- New 'free loop-length' audio recording mode (#63)
- Many AudioBuffer improvements
- Audio configuration panel refactoring
- KernelAudio improvements and cleanups
- Relaxed BPM handling when working with JACK
- Install executable to FHS compliant location (#450)
- [CI] Don't UPX binaries on macOS (#459)
- Fix Overdub protection ON by default not working (#460)
- Fix crash when moving up from a deleted folder (#455)


0.17.2 --- 2021 . 03 . 29
- New double-buffered audio engine
- Improved audio sample rendering precision
- Show tooltips when hovering over UI components
- Add .clang-format file 
- Removed support for Autotools build system
- Removed support for old raw patches
- [CMake] Use find_package command for libsamplerate
- Improved AudioBuffer move semantics
- Send time + position information to plug-ins
- Update JUCE library to version 6.0.7
- Fix crash when saving project with plug-ins in invalid state


0.17.1 --- 2021 . 02 . 01
- Better CMake dependency management
- Add CMake install rules (#422)
- Switch to GitHub Actions for CI and release builds (#440)
- Remove hardcoded 'test' folder in test suite (#432)
- Make sure macOS minimum target is set to 10.14 (#444)
- Fix crash when restarting after setting jack as an audio server (#409, #368)
- Fix crash when clicking "Cancel" button in Browser dialog (#430)
- Fix wrong action ID mapping when cloning a channel (#426)
- Fix scrambled MIDI bindings (#427)


0.17.0 --- 2020 . 11 . 15
- Add CMake build system
- VST3 support
- Show descriptive plug-in names in Plug-in List Window
- Resizable plug-in list
- New persistence mechanism for Plug-ins state
- Improved text truncation for small buttons and text boxes
- Beautify Sample Editor window
- Resizable plug-in list window
- Show descriptive plug-in name in plug-in list
- Update JUCE, version 6.0.4
- Update Catch2 to version 2.13.2
- Replace old filesystem functions in fs.h with std::filesystem
- Add VST3 SDK as git submodule
- Set minimum macOS version to 10.14
- Statically link the MSVC runtime library on Windows
- Avoid crash on opening plug-in list with invalid plug-ins
- Rewind sample channels in loop.once.bar mode on bar, if still playing (fix #403)
- Modernize log::print() function to handle std::string arguments (PR #402)
- Fix playStatus logic for ending sample channels in loop-once-bar mode (#404)
- Fix shrinking beats that could glitch the output (#361)


0.16.4 --- 2020 . 09. 19
- Support for mono inputs
- Overdub mode for Sample Channels with optional overdub protection
- Disable record-on-signal mode when sequencer is running
- Shift + [click on R button] kills action reading when "Treat one-shot channels
  with actions as loops" option is on
- Start MIDI channels automatically after action recording session
- Fix wrong sample rate conversion when project rate != system rate 
- Fix Wrong begin/end sample markers when loading a project with 
  samplerate != system.samplerate 
- Fix wrong MIDI learn mapping for master parameters
- Fix BPM button disabled after audio recording session


0.16.3 --- 2020 . 06. 15
- Non-virtual Channels architecture
- Added G_DEBUG macro
- Optimized CPU usage when playing with many channels
- Increased UI refresh rate to 30 frames per second
- Improved quantizer precision
- Simplified behavior when halting channels containing recorded actions 
- Fix wrong audio sample looping with pitch != 1.0
- Fix MIDI input master values not stored on quit
- Fix One-shot press channel mode not working via mouse 
- Fix Action recording overlap (both live and via Action Editor)
- Fix crash when loading a project with missing audio files
- Fix BPM not changing via Jack


0.16.2 --- 2020 . 02 . 18
- Switch to Json for modern C++ library for reading and writing Json data
- Resizable channels, improved version
- Drop support for raw patches (still readable for backward compatibility)
- Simplify global configuration parameters
- Simplify column data storage in patch files
- Center all micro-subwindows to screen
- Revamped MIDI learning algorithm and related UI components
- Always display 'R' button in Sample Channel
- Don't download external files for unit tests
- Optimized UI drawings for base buttons
- Move build info from 'About' window to console log
- Update RtAudio to 5.1.0
- Fix crash during audio recording after opening a project (thanks AdTb!)


0.16.1 --- 2020 . 01 . 08
- FreeBSD support
- Ability to remove empty columns manually
- Gray out bpm value when in JACK client mode
- 'Reset to init state' becomes 'close project' under File menu
- [Linux] Upgrade Travis CI Linux machine to Xenial
- Add namespaces to file system and logging functions 
- Remove unused G_quit global variable
- Fix Sample Channels in loop mode not playing automatically after audio 
  recording
- Fix action recording button status during audio recording, signal mode


0.16.0 --- 2019 . 12 . 02
- Fix columns' resizer bar height on vertical window resize
- Fix crash on MIDI learn global commands
- Fix wrong channel routing when triggering MIDI learnt commands
- Fix rewind button not rewinding sample channels in LOOP_* mode 
- Use actual buffer size from KernelAudio when loading channels from a patch
- Remove FLTK multithreading initialization


0.16.0 beta-2 --- 2019 . 11 . 11
- Remove all pthread.h leftovers 
- Fix Windows build
- Fix memory corruption on Keyboard refresh
- Fix wave size corruption while editing samples in Sample Editor
- Fix freeze when cloning a Sample Channel with a sample in it
- Fix buffer overflow when playing an edited sample
- Fix crash when loading a project with missing plug-ins
- Fix freeze when pressing 'play' during an audio recording session
- Fix play/ending UI status of MIDI channels
- Fix plug-in sorting on reload
- Fix crash when reloading a sample in the Sample Editor
- Fix messy 'R' button status when toggled
- Fix missing icons and broken checkboxes
- Optimize model updates on keyboard interaction
- Always read Columns data from patch files
- Show missing (and removable) plug-ins in Plug-in Window list
- Create default empty columns on 'Reset to initial state'
- Save relative Wave paths in project files


0.16.0 beta-1 --- 2019 . 10 . 19
- Fix macOS build error + warnings


0.16.0 beta-0 --- 2019 . 10 . 19
- New internal engine<->UI architecture 
- New persistence layer
- New MIDI queue for incoming live MIDI messages
- Switch to std::thread
- Absolute #include paths in source code
- Removed Boost parameter from Sample Channel 


0.15.4 --- 2019 . 03 . 22
- New record-on-signal option for input and action recording
- Initial support for plug-ins with mono I/O buses
- PluginHost refactoring
- Smart pointers for Wave and Plugin objects
- Remove old and deprecated input delay compensation
- Optimized audio IO processing in Mixer callback
- Atomic I/O meters with improved accuracy
- Fix memory leak when replacing samples in a Sample Channel
- Fix plug-ins ordering method when re-opening Giada
- Fix silent Sample Channel when recording actions a second time
- Fix velocity always discarded when sending key-press to Sample Channel
- Fix inability to record actions with quantizer enabled


0.15.3 --- 2018 . 12 . 24
- Action recorder refactoring
- Optional midimap parameters (thank you @tomek-szczesny)
- Support for "inaudible" MIDI lightning events (thank you @tomek-szczesny)
- Build AppImage for Linux on Travis CI instance
- Huge optimization of the AppImage binary file
- Fix Action Editor repaint on min/max zoom levels
- "Resize recording" flag has been removed
- Change text labels for channel operations
- Smarter column assignment while loading a patch/project
- Fix wrong resizer bar width between Action Editor widgets when zooming
- Can't display custom channel name in Sample Channel (fixed)
- Fix crash when cloning Sample Channel with audio data in it
- Clone channel doesn't clone channel name (fix #219)


0.15.2 --- 2018 . 09 . 05
- New sample-accurate Action Editor
- New MIDI Velocity Editor widget
- Ability to move MIDI events vertically in piano roll (i.e. change note) 
- Remove mute action recording
- Better handling of MIDI devices that send NOTEON + velocity 0 as NOTEOFF
- Avoid calls to deprecated JUCE plug-ins methods
- Removed useless pthreadGC2.dll from Windows package
- Can't kill MIDI channels (fix #197)
- Can't record MIDI actions (fix #202)
- Fix missing first beat on metronome rendering
- Fix crash on opening plug-in window on macOS


0.15.1 --- 2018 . 07 . 03
- Deep code refactoring, featuring Channels processors
- Many new unit tests added
- Simplify mutex mechanism
- Fix wrong quantizer value on patch/project load
- Remove the old, buggy and glitchy internal crossfade algorithm
- Fix many potential plug-in crashes on Linux
- Properly close plug-in window on plug-in removal 
- Improve BPM changes while running as JACK client


0.15.0 --- 2018 . 04 . 18
- Refactor audio engine into frame-based processing
- Refactor channels readers/writers into channelManager namespace
- Smarter Solo algorithm
- Fix missing .wav extension on recorded audio takes
- Fix wrong Channel status update after 'Clear all actions'


0.14.6 --- 2018 . 03 . 15
- MIDI velocity drives volume for one-shot sample channels
- FLAC and Ogg support
- Ability to use system-provided Catch library (GitHub #151)
- Update Catch to version 2
- Fix unreadable tabs title in Configuration Window (GitHub #168)
- Fix crash on opening About window
- Fix 'read actions' button behavior during ending and waiting statuses
- Fix sound card initialization on MacOS
- [Windows] Fix UI stuck on top-right corner
- [Windows] Fix browsing for directories


0.14.5 --- 2018 . 01 . 15
- OS X builds on Travis CI
- AppImage executable for Linux
- Support for multiple plug-in directories
- New directory browser for adding plug-in directories
- Update plug-in's parameters on program change in plug-in's window
- Improved MIDI action management in Piano Roll
- Simplified conditional rules in Makefile.am 
- Fix crash on MIDI learn for plug-in parameters
- Fix crash in MIDI input window if MIDI in params are 0
- Fix unwanted new action when dragging piano items in Piano Roll
- Fix crash while recording on existing project (GitHub #161) 
- Fix crash on startup in Windows build


0.14.4 --- 2017 . 10 . 28
- Renameable channels
- Portable VST path
- [Sample Editor] Sample shift tool
- [Linux/Mac] Don't skip '/' path when navigating to upper folders
- Ability to process more than one plug-in instrument at once
- Beautify Configuration Window
- Bring VST window to front when opening UI
- Save 'arm' status to patch/project file
- Revamped Beats and Bpm input windows
- Simplified audio samples' storage in project folders
- Update JUCE to version 5.1.2
- UI-less plug-in window refinements
- Update UI-less plug-in window on MIDI parameter's change
- Strip .gptc/.gprj extension from patch name
- [Sample Editor] Fix non-working 'cut' operation
- Fix missed MIDI events with more than 1 plug-in in the stack
- Fix File Browser path widget drawn incorrectly in OS X
- Fix missing MIDI learn for 'Arm channel' and 'Kill channel'


0.14.3 --- 2017 . 09 . 18
- [Sample Editor] New "reverse selection" function
- [Sample Editor] New "normalize hard" function
- [Sample Editor] New "copy to channel" function
- [Sample Editor] New "copy & paste" function
- [Sample Editor] Double click on waveform selects all
- [Sample Editor] Fix garbled characters in window's title
- [Sample Editor] Fix wrong result on "set pitch to song/bar"
- Resizable channels
- Remove calls to malloc/free in Mixer (use new/delete instead)
- Improved UI management of VST plugins
- Fix infinite loop for one shot retrig samples with quantizer > 0
- Fix wrong geChannel count while saving a patch
- Fix missing greyed-out options in Sample Channel's menu when loading a wrong
  sample
- Fix crash while audio recording with BPM set below the default 120 
- Print correct octave numbers in Piano Roll


0.14.2 --- 2017 . 08 . 14
- [Sample Editor] Audible preview (with optional loop mode)
- [Sample Editor] Frame-precise editing
- [Sample Editor] Show sample's information
- [Sample Editor] Improved fade out algorithm
- [Sample Editor] Process both left and right channel's data while drawing
- Better Wave objects handling
- Improved channels' memory management
- Improved empty columns cleanup algorithm
- Update Catch version
- Update JUCE version (5.1.1)
- Update Jansson version (2.10)
- Fix missing tempo update on reset to init state
- Fix wrong memory allocation for UI-less plugins


0.14.1 --- 2017 . 07 . 16
- Update JUCE library to 5.0.2
- Show play head in Sample Editor
- Refactor pop up menu in Sample Editor
- Many small fixes and optimizations in waveform drawing routine
- Makefile cleanup
- Fix crash while recording with beats/bars greater than 4/1 (GitHub #134)


0.14.0 --- 2017 . 05 . 29
- Sample Editor reorganized and refactored 
- Removed support for old ini-based patch files
- Improved and simplified pan algorithm
- Ability to toggle input monitoring while recording audio
- Lots of code refactoring
- Convert all .h headers to C++ headers
- Update Libsndfile to version 1.0.28
- Fix crash when recording audio
- Fix wrong file path when exporting samples
- Fix a bug that prevented begin/end handles to work in Sample Editor
- Fix Sample Editor's grid value not being stored properly on close


0.13.4 --- 2017 . 04 . 23
- Removed support for old ini-based MIDImap files
- Initial support for channel-based MIDI filtering
- New Orphaned MIDI events in Piano Roll editor
- Improve action filtering in Piano Roll editor
- Lots of code refactoring
- New test suite for Action Recorder
- Fix obscure bug when overdubbing actions and a null loop occurs
- Fix "clear all actions" menu refresh when removing items on Piano Roll


0.13.3 --- 2017 . 03 . 25
- Strip VST folder from Git repository
- Fix 'Close' button's position inside MIDI input window
- Update RtMidi to version 2.1.1
- Improve 'free channel' function (GitHub #105)
- New 'Clock' structure for timing operations
- New Jack implementation with BPM sync and Rewind (GitHub #89)
- Fix missing tracker reset on 'free channel' function (GitHub #99)


0.13.2 --- 2017 . 01 . 14
- MIDI learn for plugins parameters
- Toggle hidden files in File Browser
- Fix broken compilation when build without VST support
- Make sure PluginChooser window has a sane size
- Decouple Recorder from any global variable
- Better source code organization
- Make plugin creation more robust
- More source code reorganization
- Fix crash on clicking scrollbar arrows (GitHub #53)
- Fix crash when doubling/dividing length while recording (GitHub #110)


0.13.1 --- 2016 . 11 . 16
- Input MIDI to MIDI channels/plugins
- Refinements to show/hide 'R' button's dynamics
- Increase piano roll items' height
- Set input volume to max by default
- Start live-recorded sample channels right away
- Avoid potential crashes when loading samples on running channels
- Generate metronome during output post-processing
- Better widgets' layout in Sample Editor
- Lots of source code optimizations and cleanups
- Fix inverted 'R' button's status (GitHub #94)
- Better handling of 'R' button's status when the sequencer is off (GitHub #95)
- Fix non-playing samples if live-recorded and 'R' button is on (GitHub #93)
- Reset button statuses once channels have been freed (GitHub #100)
- Fix missing ASIO and WASAPI APIs on Windows (GitHub #96)
- Missing RtMidi libs on Linux (GitHub #102)
- Fix fade-in/fade-out editing not triggering alert on save (GitHub #101)


0.13.0 --- 2016 . 09 . 20
- Deep file browser refactoring
- Save browser's scroll position and last item selected on opening
- Load patches/projects/samples on double click
- 64 bit builds for Windows
- Prevent deprecated patch from crashing if a plugin is not found in the stack
- Force logger to flush to file on Windows
- Add more default values for windows' dimensions and positions
- Avoid crashes on Configuration panel if no midimaps were selected
- Fix missing keyRelease actions in action editor
- Update JUCE to version 4.2.3
- Don't include JUCE on tests without VST support (GitHub #75)
- Fix compilation errors on GCC 6 (GitHub #82)
- Fix includes on OSX (GitHub #92)
- Fix wrong channel's actions count that prevented "R" button to be toggled
  properly
- Fixed a bug that prevented actions on frame 0 to being properly reproduced
- Make Recorder a proper class
- Better naming convention for ActionEditor's children classes
- Source code reorganization


0.12.2 --- 2016 . 06 . 02
- Update RtAudio to version 4.1.2
- Add WASAPI support on Windows
- Sortable plugins list
- Simplify custom RtAudio build and inclusion on Linux
- Fix crashes on startup on OS X El Capitan
- Store position and size of Available Plugins window
- Untangle Channels' code from global variables


0.12.1 --- 2016 . 05 . 06
- Show percentage progress for plugin scan
- Notify if plugins are missing
- Notify if unknown plugins are present
- Fix potential segfault on MasterIn/MasterOut plugins loading
- Proper cleanup of JUCE resources
- Internal refactoring on PluginHost's global variables


0.12.0 --- 2016 . 03 . 07
- Port to JUCE Framework for audio plugin management
- Increase global font size
- Minor UI fixes and cleanups
- Add ability to run tests outside Travis CI
- Switch to C++11
- 64 bit binaries for OS X
- Use new constant for global font size


0.11.2 --- 2016 . 01 . 16
- New JSON-based midimap files
- Add new channel by right-clicking anywhere on a column
- Show warning if patch is using the deprecated file format
- Do not force 32 bit compilation on OS X
- Fix warnings and errors on GCC 5.3
- Fix a bug that prevented MIDI Jack from being selected on Linux


0.11.1 --- 2015 . 12 . 22
- Ability to clone channels
- New JSON-based configuration file
- Port all vectors from old gVector to std::vector
- Deactivate all other MIDI fields when changing MIDI system in Config window
- Minor optimizations in configuration panel, Audio tab
- Assume 'none' as default sound system
- Include Catch header file in source package
- Update Travis CI environment to Ubuntu Trusty
- Fix missing sanitization after reading configuration file
- Fix garbage text in device info window
- Fix wrong config value if no midimaps are available
- Fix garbage text while printing device and port names


0.11.0 --- 2015 . 12 . 02
- New JSON-based patch system
- Properly store column width in patch
- Port all const char* strings to std::string in patch/project glue layer
- Switch to SemVer-like internal versioning system
- More source code reorganization
- Fix potential memory leaks in Mixer
- Fix missing static link of RtMidi on Linux
- Unable to store pitch values > 2.0 (fixed)
- Missing assigned key after opening patch (fixed)


0.10.2 --- 2015 . 10 . 21
- Setup Travis CI automated builds
- Add base framework for unit testing (with Catch)
- Improve behavior of Loop Once family when the sequencer is halted
- Fix empty sample path in sample channels when saving a Project
- Fix disabled "edit actions" for sample channels
- Fix missing pthreadGC2.dll in Windows build


0.10.1 --- 2015 . 08 . 26
- Massive source folders refactoring
- Improved usability of "play" buttons for channels
- Remove support for patches created with Giada < 0.6.x
- Fix check for configured soundsystem (would break compilation on g++5)
- Small fixes and cleanup in Makefile.am


0.10.0 --- 2015 . 07 . 05
- MIDI lightning output
- Other minor fixes


0.9.6 --- 2015 . 05 . 11
- Keyboard binding for MIDI channels
- Support for multiple files in drag-n-drop operations
- Different color for wait/end statuses
- Small improvements to Keyboard grabber widget
- Fix random crashes with Jack enabled
- Fix weird behavior with multiple drag and drop
- Code refactoring


0.9.5 --- 2015 . 03 . 28
- Better column resize algorithm
- New patch loading system with permanent MIDI mapping
- Ability to clear assigned keys (keyboard mode)
- Improved zoom icons in editors
- Fix deprecation warning in configure.ac


0.9.4 --- 2015 . 02 . 24
- Drag-n-drop now works also in existing channels
- Store 'resize recordings' flag in giada.conf
- Better management of duplicate samples
- Add more VST debug information
- Minor fixes and tweaks


0.9.3 --- 2015 . 02 . 01
- New GUI improvement: responsive and resizable columns
- Upgrade to FLTK 1.3.3
- More robust column handling mechanism
- Support for MIDI devices without note-off message (@blablack)
- Fix segfaults when saving a patch with missing plugins
- Fix many minor graphical bugs
- Fix wrong vector assignment in MIDI send event
- Fix reloaded patches with no right tempo/beats displayed
- Fix random odd frames when adding/moving events in Piano Roll
- Minor internal cleanup


0.9.2 --- 2014 . 11 . 29
- New grid layout in Sample Editor
- Load samples via drag n drop
- Add new utility functions: gTrim and gStripFileUrl
- Fix "normalize" button position in Sample Editor
- Minor waveform drawing optimizations
- Add missing files for RtAudio-mod compilation
- All one-shot mode, if fired manually, get the first frame truncated (fixed)


0.9.1 --- 2014 . 09 . 24
- Bring back custom version of rtAudio in source package
- Automatically turn up volume when adding new channel
- Updated 'misc' tab in configuration panel
- Fix startup crash on OS X
- Fix missing jack headers


0.9.0 --- 2014 . 08 . 18
- New full-screen GUI
- Multi-column support
- Advanced logging system
- Upgrade to RtAudio 4.1.1 and RtMidi 2.1.0
- Removed embedded RtAudio (thanks to Arty)
- Fix wrong processing of VST MIDI events on 64 bit version
- Fix stretched buttons when resizing sample editor window
- "Clear all samples" destroys channels (fixed)
- "Free channel" messes up loop / mute buttons (fixes)
- Fix potential recordings with odd frames


0.8.4 --- 2014 . 03 . 27
- New mode 'Loop Bar Once'
- Several small improvements and cleanups to internal utils functions
- Fixed missing title in several subwindows
- (win) Fix runtime error when loading a new project
- Fix chan reset when clicking on waveform
- Properly close subwindows after a channel has been deleted
- Fix 'reload' button not working for samples with updated names


0.8.3 --- 2014 . 02 . 14
- Experimental MIDI timing output with MTC and MIDI clock
- Expose Sequencer x2 and /2 via MIDI
- New pitch operators x2 and /2
- Internal xfade process restored
- "set key..." becomes "setup keyboard input" for sample channels
- MIDI events are now saved as unsigned int in patch
- Same expression on both sides of '|' in recorder.cpp (fixed)
- Muted channels leak some glitches on 'kill' event (fixed)
- Piano roll can't be edited anymore if beats == 32 (fixed)
- Noise when adding new MIDI channel (fixed)
- Boost and Normalize not working (fixed)
- Multiple copies of every file used by the patch (fixed)
- Samples with -1, -2, ... -n suffix are not included in patch (fixed)
- Segfaults when quantizing samples (fixed)


0.8.2 --- 2014 . 01 . 13
- Pitch control exposed via MIDI
- New tools in Sample Editor (linear fade in/out, smooth edges)
- Implemented vstEvent->deltaFrames, gaining more precision with vst
	MIDI events
- Add Fl::lock/Fl::unlock dynamics to glue_ calls where needed
- Avoid pitch sliding when changing pitch of a sample in status OFF
- Update copyright info in source files
- Internal fade in and fade out restored
- Add 'Giada' keyword to desktop file
- Fix annoying glitches when playing very short samples
- Fix random crashes when controlling giada via MIDI
- Fix missing MIDI mapping for read-actions button


0.8.1 --- 2013 . 12 . 09
- New, high-quality pitch control based on libsamplerate
- New set of functions 'spread sample to beat/song'
[known issues]
- Internal crossfades have been temporarily disabled. Some clicks may
	occur


0.8.0 --- 2013 . 11 . 03
- Initial MIDI input support
- Fix freeze when recording audio inputs on a second channel
- Fix 'R' button to show up even if the channel has no actions
- Fix weird drawings of keypress actions in action editor
- Free channel: delete 'R' button as well
- Shift+key does not kill loop mode channels in a wait status
- Fix issue with 'R' button and newly added actions
- Remove "left"/"right" labels from main buttons


0.7.3 --- 2013 . 09 . 14
- Experimental 64 bit compilation (Linux only)
- Massive internal cleanup of channel/gui channel layers
- Set default mode to full volume on sample load
- Set default mode to oneshot basic
- Faster drawings in piano roll
- Visual aids in piano roll
- Scroll to pointer in piano roll
- Several minor improvements in piano roll's usability
- Revised VST Carbon window popup system
- Minor improvements in startInputRec/stopInputRec procedure
- Fix compile error using local type Plugin* in Channel's constructor
- Fix segfault in OSX when working with VST windows


0.7.2 --- 2013 . 07 . 27
- Initial MIDI output support
- Mute now affects channels with VSTi signals
- Lots of deb package improvements
- Complete rewrite of VST GUI part on OS X
- Don't send MIDI mute on sample channels
- Send MIDI mute for MIDI channels in play mode
- Fix wrong looping due to VST processing in mixer::masterPlay
- Fix jack crashes when using Giada with ALSA
- Fix VST random crashes on OSX, bus error
- Fix input device set to -1 after a system change


0.7.1 --- 2013 . 06 . 27
- Initial Jack Transport support
- Send global note off when sequencer is being stopped
- Send note off when deleting notes in Piano Roll
- Store position and size of Piano Roll in conf file
- Avoid overlap MIDI notes in Piano Roll
- MIDI channel refactoring
- MIDI channels now behave like loop-mode ones
- Fix graphical bugs in Action Editor, sample mode
- Fix refresh issue in Piano Roll when deleting items
- Lots of invisible cleanups and improvements


0.7.0 --- 2013 . 06 . 05
- Initial MIDI output implementation
- Initial VSTi (instrument) support
- New piano roll widget in action editor
- New chan mode: MIDI vs SAMPLE
- Fix E-MU Tracker Pre not correctly listed in audio in/output


0.6.4 --- 2013 . 05 . 07
- Resizable plugin parameter window
- New and standard package name format <name>-<version>.<ext>
- Implement RtAudio::getCompiledApi() to fetch compiled APIs
- Implement audioMasterGetSampleRate, audioMasterGetLanguage VST opcodes
- Add drop-down menu for buffer size values in config panel
- Enhance project portability between OSes
- Lots of fixes and improvements for VST strings and parameters
- Avoid segfault when loading recs from a patch with files not found
- Always remember selected program when shifting up/down plugins
- Fix wrong size of single_press displayed in action editor
- Fix volume actions resized with value set to zero
- Fix volume envelope always over the cover area
- Fix src package extracts to current dir
- Fix segfault in loadpatch process if plugin GUIs are open
- Fix segfault when closing patch with plugins in BAD status


0.6.3 --- 2013 . 04 . 23
- New 'solo' button
- Portable project system
- New 'Single Endless' channel mode
- GUI enhancements for channels in WAIT or ENDING status
- Minor fixes & cleanups


0.6.2 --- 2013 . 04 . 05
- New volume envelope widget
- Zoom with mouse wheel in the action editor
- Graphical enhancements & speedups for the action editor
- Loop-repeat doesn't stop when put in ending mode (fixed)
- Fix draw errors when zooming too much the action editor
- Set silence in wave editor messes up the waveform (fixed)
- Wrong slashes in file path when saving a patch in Windows (fixed)
- Many, many code improvements and bugs fixed


0.6.1 --- 2013 . 03 . 21
- Unlimited number of channels
- Deep internal refactoring, mixer/GUI layers
- Fix random crashes on exit
- Fix crashes when closing Giada with VST windows opened
- Always free Master In plugin stack on exit
- Lots of other minor bugs fixed and small enhancements


0.6.0 --- 2013 . 03 . 02
- New, full-screen, redesigned sample editor
- Zoom with mouse wheel in sample editor
- Use kernelAudio::defaultIn/defaultOut for DEFAULT_SOUNDDEV_OUT
- Volume knob in main window now updates the editor
- Sound system issues in OS X (fixed)
- Output device info dialog refers to wrong device (fixed)


0.5.8 --- 2013 . 02 . 07
- Internal samplerate conversion (with libsamplerate)
- Bring channels automatically to full volume on sample load
- Ability to set the audio device frequency
- New "internal mute" feature
- fix for deprecated VST opcode 14
- fix deb package issues on Ubuntu 12.10 / KXStudio


0.5.7 --- 2013 . 01 . 21
- visual grid + snapping in the action editor
- implement more audioMasterCanDo's in pluginHost
- limit zoom in actionEditor
- revise zoom behavior in actionEditor, now more comfortable
- fix forward declaration & inclusion of several headers
- implemented VST opcode 32
- implemented VST opcode 33
- implemented VST opcode 34
- update website link in tar files
- update copyright info for 2013


0.5.6 --- 2013 . 01 . 03
- New overdub mode for live recording
- Support for VST programs, aka presets
- Lots of VST opcodes implemented
- Fix crash when removing a plugin from the stack
- Fix pops when going to beat 0
- Fix compilation issues without --enable-vst
- Many invisible optimizations and small bugs fixed


0.5.5 --- 2012 . 12 . 15
- "Hear what you're playing" feature
- Fx processing on the input side
- Ability to add different action types (Action Editor)
- Desktop integration on Linux (via deb package)
- Upgrade to FLTK 1.3.2
- Remove "the action might stop the channel" when loading new samples
- Fix wrong positioning of zoom tools (Action Editor)
- Fix unwanted interactions on the grey area (Action Editor)
- Fix wrong memory alloc during the VST processing
- VST don't show up in OS X (fixed)
- Minor internal refactoring + bugfixing


0.5.4 --- 2012 . 11 . 24
- VST GUI support
- Better subwindow management
- Implemented many other VST opcodes
- Missing plugins are now shown in the list with a 'dead' state
- Refresh action editor when changing beats (via beat operator or
  beat window)
- Graphical improvements in the action editor
- Resizable action editor doesn't work well (fixed)
- Fix auto fadeout for SINGLE_PRESS channels
- Fix compilation without --enable-vst
- Fix for a wrong prototype definition of the VST hostCallback


0.5.3 --- 2012 . 10 . 26
- Live beat manipulators (x2)(/2)
- New sub-windows management, faster and more comfortable
- New optional hard limiter on the output side
- Action Editor window recalls x,y,w,h zoom and position
- Usability improvements while handling an action (action editor)
- Refresh actionEditor window when switching channel mode or delete
	actions
- Unable to delete a killchan action (action editor) (fixed)
- Don't show ACTION_KILLCHAN in a singlepress channel (action editor)
- Libsndfile no longer statically linked in Linux
- Fixed a typo in config: "when the sequeCer is halted"
- redefinition of DEFAULT_PITCH in wingdi.h (windows) (fixed)
- Upgrade to FLTK 1.3.0
- Other internal optimizations
- Other small bugs fixed


0.5.2 --- 2012 . 10 . 05
- Add ability to handle actions for loop-mode channels
- Add ability to record live mute actions for loop-mode channels
- Lots of live action recording improvements
- Enhanced usability for the action editor
- More verbose output if kernel audio fails to start
- Several internal optimizations


0.5.1 --- 2012 . 09 . 13
- First implementation of the Action Editor
- Added compatibility with Ubuntu >= 10.04


0.5.0 --- 2012 . 07 . 23
- New custom project folder (.gprj)
- Sample names are now made unique
- Fixed unwanted time stretching while exporting a mono sample
- Lots of minor internal improvements


0.4.12 --- 2012 . 07 . 01
- VST parameters and stacks are now stored in patch file
- Upgrade to RtAudio 0.4.11
- PulseAudio support in Linux (thanks to RtAudio 0.4.11)
- Revised .deb package
- Enhanced "normalize" function in wave editor
- Several memory issues fixed
- Internal enhancements and minor bugs fixed


0.4.11 --- 2012 . 06 . 10
- VST stack for each channel
- Custom paths for plugins, samples and patches
- Crash in config panel if device is busy (fixed)
- Graphical bug in the input meter (fixed)
- ParamLabel added in the VST parameter list


0.4.10 --- 2012 . 05 . 30
- Ability to shift up an down VST plugins
- Enhanced patch/conf architecture
- Ability to edit a sample while playing
- Mutex controls in VST processing
- Lots of security issues fixed while changing pitch dynamically
- Enhanced sub-window system
- Several minor bugs fixed


0.4.9 --- 2012 . 05 . 12
- No more mandatory inputs
- Pitch value properly stored inside the patch
- Several small VST host improvements
- Enhanced window management
- Ability to browse files while playing with main GUI (non-modal browser)
- Improved error checking in KernelAudio
- Wrong style for lower scrollbar in Browser (fixed)
- Fixed compilation on 64 bit systems (thanks to Speps@Archlinux)
- Samplerate no longer hardcoded, auto-detected with JACK
- Minor internal improvements and bugfixing


0.4.8 --- 2012 . 04 . 21
- Initial VST support (experimental)
- Pitch controller (experimental, no filtering)
- OSX bundles are now correctly handled by the file browser
- Fixed several memory leaks
- Minor internal improvements


0.4.7 --- 2012 . 03 . 31
- Cut, trim & silence operations in sample editor
- New "Reload sample" button added
- Lots of optimizations in the waveform drawing routines
- The sample is no longer editable while in play mode
- Fixed potential startup crashes while using Giada with Jack Audio
- Other minor fixes applied to the configuration panel
- Fixed compilation on 64 bit systems (thanks to Speps@Archlinux)


0.4.6 --- 2012 . 03 . 11
- New device information panel
- The device configuration now shows only active and available devices
- Channel panel no longer pops up during a recording process
- GUI beautifications and other minor graphical fixes
- Program icon added in all subwindows
- Action records no longer available during a take, and vice versa
- Fixed a serious bug that swapped input and output devices
- Fixed loop behavior in ending mode
- Fixed clicks when stopping a muted channel in loop


0.4.5 --- 2012 . 02 . 25
- Complete GUI redesign
- New "start/stop action recs" button
- Lots of internal cleanups and micro refactorings
- Small drawing glithes in Editor and status box (fixed)
- An invalid patch puts Giada to init state (fixed)
- Fixed button repeat on start/stop, action rec, input rec
- Checks against takes with unique name
- Message "this action may stop the channel" always shown (fixed)
- Channel no longer freeable while a take is in progress


0.4.4 --- 2012 . 02 . 04
- New input/output channel selector
- Rewind bypasses the quantizer if triggered via mouse (fixed)
- Fixed library paths in configure and makefile (thanks to Yann C.)
- Added AUTHORS and NEWS files to the source package (thanks to Yann C.)
- More robust sample export procedure
- Issues with mute buttons when opening a patch (fixed)
- Several usability improvements
- Minor code cleanups and optimizations


0.4.3 --- 2012 . 01 . 21
- New "save project" feature
- Ability to export a single sample to disk
- More feedback when removing/clearing actions and samples
- Sequencer starts automatically when action-rec button is pressed
- Alert if patch name is empty while saving it
- Channels now store internally the name of the samples
- Missing "--no devices found--" in input devices menu (fixed)
- Alert added if there are no empty channels for recording
- "Edit->Clear all actions" no longer works (fixed)
- END button could be used as a channel trigger (fixed)
- Recorders are available even if device status is wrong (fixed)
- Missing sample rewind if channel is muted (fixed)
- Quantizer doesn't work if framesize is odd (fixed)
- Random segfault when closing Giada (fixed)
- Lots of code cleanups
- Other minor improvements and optimizations


0.4.2 --- 2012 . 01 . 09
- Live sampling from external input with meter and delay compensation
- Check against uneven values and overflow in buffersize field
- Wrong normalized values if volume level is 0.0 (fixed)
- Boost dial goes crazy if normalized > 20.0 dB (fixed)
- Boost dial goes crazy if normalized < 0.0 dB (fixed)
- Unwanted noise click if a muted channel is being rewinded (fixed)
- Mute doesn't work well for single-shot samples (fixed)
- Wrong FLTK headers (fixed, thanks to Yann C.)
- Moving chanStart/chanEnd swaps stereo image (fixed)
- Reset to init state doesn't reset mute buttons (fixed)
- Wrong chanStart value if > 0 (fixed)


0.4.1 --- 2011 . 12 . 07
- Complete mixer engine refactoring
- Faster audio buffer allocation
- Global beat system revisited
- Autocrossfade between samples is now enabled by default
- No more recorded actions on odd frames
- Unintentional channel swapping fixed
- Unable to list all sound systems and sound devs under OSX (fixed)
- Missing graceful stop of audio streaming under OSX (fixed)


0.4.0 --- 2011 . 11 . 16
- Support for all major uncompressed file formats (with libsndfile)
- Enhanced mono > stereo conversion
- Fixed drawing issues for the start/stop labels inside the waveform
- Enhanced backward compatibility with old patches
- Support for compilation on OS X and Windows


0.3.6 --- 2011 . 11 . 02
- Initial Mac OS X release
- (Windows) Ability to list and browse all active drives
- Change some internal routines plus minor optimizations
- Added -pedantic and -Werror flag to the compiler
- Crash if clicking on mute in an empty channel (fixed)
- Chan status changes if an empty channel is being muted (fixed)


0.3.5 --- 2011 . 10 . 22
- Pan controller added
- New GNU-style source code packaging
- Revamped .deb package
- Program icon missing under Windows (fixed)
- Crash if a sample in patch is missing from the filesystem (fixed)
- Unable to rewind to beat 1 if quantizer is on and seq stopped (fixed)
- Several minor glitches fixed


0.3.4 --- 2011 . 10 . 10
- Full source code released under GPL license
- Autosmooth is now toggleable via setup
- Faster loading process of patch files
- Various internal cleanups and optimizations
- Fixed incorrect reading of boost values from patch
- Fixed a potential bug that prevented the config panel to appear
- Fixed stereo swap bug
- Minor graphical revisions


0.3.3 --- 2011 . 09 . 28
- New "normalize" function
- More editing tools added inside the sample editor
- Waveform beautifications
- Fixed interaction bugs for boost and volume controls


0.3.2 --- 2011 . 09 . 19
- New "mute" button inside the main window
- Waveform is now updated when the boost value changes
- Zoomin/zoomout relative to the scrollbar position
- Fixed garbage output if the volume was "-inf" (windows version)
- Fixed several rendering issues for short waveforms


0.3.1 --- 2011 . 09 . 12
- Boost volume + fine volume control in sample editor
- Start/End handles inside the editor are now draggable via mouse
- Fixed scrollbar issues in sample editor
- Start/end points are now always drawn in the foreground
- Waveform no longer overflow if a value is greater than the window
- (linux) giada.conf is saved inside the hidden folder /home/<USER>
- patch loading process is now faster and cleaner
- Update to rtAudio 4.0.10


0.3.0 --- 2011 . 09 . 01
- New sample editor window
- Ability to set start/end points within a sample
- Update to rtAudio 4.0.9
- Fixed an string overflow inside a patch
- Fixed a missing memory free if a sample is unreadable
- Several internal updates and optimizations


0.2.7 --- 2011 . 07.  22
- New way to handle recorded channels as loops
- Fixed retrig for backspace key (rewind)
- Enhanced rewind with quantization support
- Main and alert windows now appear centered on screen
- Sanity check against old patches without metronome information
- Rewind now affects loops in rec-reading mode


0.2.6 --- 2011 . 07 . 11
- Internal metronome
- Fixed some glitches in config panel
- Minor cleanups


0.2.5 --- 2011 . 06 . 20
- Configuration panel redesign
- Several new control options
- Progress feedback when loading patches
- Internal optimizations
- Updated docs


0.2.4 --- 2011 . 06 . 08
- New loop repeat mode
- Ability to save patches anywhere in the filesystem
- Sub-beat management
- Sound meter has been revisited and improved
- Several patch enhancements
- Core audio optimizations


0.2.3 --- 2011 . 05 . 18
- ASIO support for Windows version
- Enhanced security when reading values from a patch
- Ability to disable the recordings when the sequencer is paused
- Master volume and rec status are now saved inside the patch
- Device selection fixed and improved
- Sequencer flickering in Windows has been fixed
- Feedback added if a sample from a patch is unreadable or corrupted
- Minor internal optimizations


0.2.2 --- 2011 . 05 . 04
- New open-source patch system
- A patch can now be loaded from any location of the filesystem
- Enhanced file browser coords system
- Lots of minor improvements to the sample loading/unloading procedure
- (win) Init path of file browser now starts from %userProfile%/Desktop
- Wrong handling of "/" chars fixed in config menu
- Fixed potential hangs on quit
- Fixed clicks when stopping sequencer/sample
- Minor gui beautifications


0.2.1 --- 2011 . 04 . 26
- Windows version


0.2.0 --- 2011 . 04 . 19
- Full JACK and ALSA support with RtAudio
- New list of sound devices in menu window
- Enhanced shutdown procedure to prevent potential crashes
- Some GUI glitches fixed
- Fixed random locks when the screensaver is active


0.1.8 --- 2011 . 04 . 13
- new functions: free al samples/recordings, reset to init patch
- main menu redesign
- the file browser is now resizable
- GUI feedback for samples in play mode
- some fixes when unloading a sample


0.1.7 --- 2011 . 04 . 07
- Ability to remove only action recordings or mute recordings
- Shift+key now stops the sample if the master play is deactivated
- Frame 0 was always processed at the end of the sequencer
- Minor internal improvements


0.1.6 --- 2011 . 03 . 29
- Autocrossfade to prevent clicks
- Internal improvements and bugfixing


0.1.5 --- 2011 . 03 . 10
- decimal bpm adjustment
- ability to shrink/expand actions when changing the global beats
- improved GUI for beats and bpm controllers
- improved routines for action management
- actions are now updated when you change bpm


0.1.4 --- 2011 . 03 . 04
- ability to save recorded actions
- status box now shows if a recorded chan is deactivated
- recorder is reset correctly when you load a new patch
- minor improvements


0.1.3 --- 2011 . 02 . 26
- action recorder (first implementation)
- quantization procedure slightly optimized
- minor graphical adjustments
- expanded documentation


0.1.2 --- 2011 . 02 . 08
- master volume controller
- improved sound meter with more accuracy
- improved verifications when reading or writing a patch
- beat counter is now always reset to 1 after a patch is loaded
- made loading wave files more robust, plus memory optimizations
- minor crashes fixed


0.1.1 --- 2011 . 01 . 26
- expansion to 32 channels
- GUI restyling
- live quantizer
- fixed wrong handling of "mute" value when loading a patch
- minor internal improvements


0.1.0 --- 2011 . 01 . 18
- ability to mute channels
- stop and rewind buttons now affect only channels in loop mode
- undo for ending loops
- internal patch improvements to provide backward compatibility
- better behaviour when exceeding the total amount of available memory
- fixed random reversals of stereo field at the end of the beat bar
- fixed a potential segmentation fault when freeing a sample


0.0.12 --- 2011 . 01 . 11
- ability to free a channel
- "stop" button to suspend the general program
- new "stop-to-end" mode for looped channels
- new "full stop" key combination
- enhanced mouse interaction
- minor bugfixing


0.0.11 --- 2010 . 12 . 28
- customizable keys
- GUI layer optimizations and improvements
- overwrite confirmation when saving a patch
- the browser always displays the patch folder when loading a new patch
- browser url is now read-only to prevent manipulations


0.0.10 --- 2010 . 12 . 16
- new "single-mode retrig" mode added
- expansion to 16 channels
- new advanced file browser with the ability to navigate the filesystem
- audio configuration now uses the "default" device, if not changed
- graphical restyling for audio channels
- fixed a random crash on startup, due to a wrong thread synch


0.0.9 --- 2010 . 12 . 08
- new loop once mode
- new graphical beat meter
- rewind-program button added
- heavy buttons and controls restyling
- reinforced header verification when a new patch is opened for reading
- some bugfixing for the loading procedure of a patch
- fixed a potential crash while a new sample is being loaded


0.0.8 --- 2010 . 11 . 28
- fixed a critical crash while loading a sample
- GUI warning when loading a sample or a patch into an active channel
- little optimization during the search for data into waves
- all popup windows are now modal (always on top)
- fixed a potential crash in case of malformed wave files


0.0.7 --- 2010 . 11 . 18
- new peak meter with clip warning and system status report
- any "ok" button is associated to the "return" key (for fast inputs)
- graphical improvements for checkboxes, buttons, smaller fonts in browsers
- graphical feedback for missing samples
- internal optimizations


0.0.6 --- 2010 . 11 . 01
- new 32 bit floating point audio engine
- support for any wave bit-rate, from 8 bit pcm to 32 float
- Giada now prompts when a sound card error occurs
- removed the hard-limiting system, now useless
- the "save patch" panel now shows the actual patchname in use
- alphabetic sort into the file browser
- fixed an annoying gui flickering
- patch volume information are now handled correctly
- minor internal optimizations
- fixed a memory leak when loading a new patch
- other memory optimizations


0.0.5 --- 2010 . 10 . 21
- Patch-based system: load/save your setup from/to a binary file
- New audio configuration panel
- New configuration file (giada.conf) where to store data
- Complete implementation of the double click startup
- Fixed a bug related to the confirm-on-quit window
- Minor GUI beautifications
- Extended documentation


0.0.4 --- 2010 . 10 . 11
- New internal sample-accurate loop engine
- Ability to configure the period size through ini file
- First implementation of the double click startup
- Debug information are now properly tagged, reporting the interested layer


0.0.3 --- 2010 . 10 . 02
- (giada) New official logo
- (giada) Ability to load single-channel samples
- (giada) Capital letter consistency between GUI buttons
- (giada) Added "cancel" button to the browser window
- (giada) Endianness verification
- (giada) Cleanup of the audio initialization procedure
- (giada) Several internal optimization for audio playback
- (giada) ALSA layer now tells if an underrun occurs
- (giada) Internal memory allocation improvements
- (giada) Fixed an unallocated hardware parameter into ALSA configuration
- (wa) Information about wave endianness
- Added a "Requirements" section to the readme file


0.0.2 --- 2010 . 09 . 17
- (giada) More visual feedbacks if a key is pressed
- (giada) Added a graphical alert if a sample is in an incorrect format
- (giada) Confirm on exit
- (giada) Graphical improvements for the browser window
- (giada) Browser window doesn't close itself anymore if a sample format is incorrect
- (giada) Added "-- no sample --" for empty channels
- (giada) Startup no longer fails if a sample from the ini file is not found
- (giada) Internal optimization for the sample loading routine
- (giada) More graphical consistency between subwindows
- (giada) The sample name is now truncated to fit into its box, preventing overflow
- (giada) Other minor GUI tweaks
- (giada) Internal memory improvements to prevent a bad bug of allocation with malformed wave files
- (wa) More information about sample size
- (wa) Added calculations and comparison between data sizes


0.0.1 --- 2010 . 09 . 06
(initial release)
