<p align="center">
	<img src="https://raw.githubusercontent.com/monocasual/giada/master/extras/giada-logotype.png" alt="Giada - Your Hardcore Loop Machine">
</p>

<p align="center">
<strong>Giada - Your Hardcore Loop Machine</strong> | Official website: <a href="https://www.giadamusic.com">giadamusic.com</a> | <a href="https://github.com/monocasual/giada/actions?query=workflow%3A%22Continuous+integration%22"><img src="https://github.com/monocasual/giada/workflows/Continuous%20integration/badge.svg" alt="Build status"></a>
</p>

## What is Giada?

Giada is an open source, minimalistic and hardcore music production tool. Designed for DJs, live performers and electronic musicians.

<p align="center">
✦✦✦ <a href="http://www.youtube.com/user/GiadaLoopMachine">See Giada in action!</a> ✦✦✦
</p>

![Giada Loop Machine screenshot](https://giadamusic.com/images/giada-canvas.png)

## Main features

* Giada is a loop machine — build your performance in real time by layering audio tracks or MIDI events, driven by the main sequencer;
* Giada is a sample player — load samples from your crates and play them with a computer keyboard or a MIDI controller;
* Giada is a song editor — write songs from scratch or edit existing live recordings with the powerful Action Editor, for a fine-tuned control;
* Giada is a live recorder — record sounds from the real world and MIDI events coming from external devices or other apps;
* Giada is an FX processor — process samples or audio/MIDI input signals with VST instruments from your plug-ins collection;
* Giada is a MIDI controller — control other software or synchronize physical MIDI devices by using Giada as a MIDI master sequencer.

### And more:

* Ultra-lightweight internal design;
* multi-thread/multi-core support;
* 32-bit floating point audio engine;
* ALSA, JACK + Transport, CoreAudio, ASIO and DirectSound full support;
* unlimited number of channels (optionally controllable via computer keyboard);
* BPM and beat sync with sample-accurate loop engine;
* MIDI input and output support, featuring custom [MIDI lightning messages](https://github.com/monocasual/giada-midimaps);
* super-sleek, built-in Wave Editor for audio samples and Piano Roll editor for MIDI messages;
* automatic quantizer;
* portable project storage system, based on super-hackable JSON files;
* support for all major uncompressed file formats;
* test-driven development style supported by [GitHub Actions](https://github.com/monocasual/giada/actions) and [Catch](https://github.com/philsquared/Catch)
* under a constant stage of development;
* 100% open-source GPL v3.

## License

Giada is available under the terms of the GNU General Public License.
Take a look at the COPYING file for further information.

## Documentation

Documentation is available online in the [user guide page](https://www.giadamusic.com/documentation-index).

An ever-growing collection of tutorials (both text and video) and live demos is available in the [tutorials & media page](https://www.giadamusic.com/media).

Found a typo or a terrible mistake? Feel free to clone the [website repository](https://github.com/monocasual/giada-www) and send us your pull requests.

## Build Giada from source

We do our best to make the compilation process as simple as possible. You can find all the information in the [compiling from source](https://www.giadamusic.com/documentation-compiling-from-source) chapter from the user guide.

## Bugs, requests and questions for non-developers

Feel free to ask anything in the [discussions area](https://github.com/monocasual/giada/discussions).

## Copyright

Giada is Copyright (C) 2010-2025 by Giovanni A. Zuliani | Monocasual Laboratories

Giada - Your Hardcore Loopmachine is free software: you can redistribute it and/or modify it under the terms of the GNU General Public License as published by the Free Software Foundation, either version 3 of the License, or (at your option) any later version.

Giada - Your Hardcore Loopmachine is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with Giada - Your Hardcore Loopmachine. If not, see <http://www.gnu.org/licenses/>.
