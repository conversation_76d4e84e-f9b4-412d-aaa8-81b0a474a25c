/* -----------------------------------------------------------------------------
 *
 * Giada - Your Hardcore Loopmachine
 *
 * -----------------------------------------------------------------------------
 *
 * Copyright (C) 2010-2025 Giovanni <PERSON> | Monocasual Laboratories
 *
 * This file is part of Giada - Your Hardcore Loopmachine.
 *
 * Giada - Your Hardcore Loopmachine is free software: you can
 * redistribute it and/or modify it under the terms of the GNU General
 * Public License as published by the Free Software Foundation, either
 * version 3 of the License, or (at your option) any later version.
 *
 * Giada - Your Hardcore Loopmachine is distributed in the hope that it
 * will be useful, but WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Giada - Your Hardcore Loopmachine. If not, see
 * <http://www.gnu.org/licenses/>.
 *
 * -------------------------------------------------------------------------- */

#ifndef G_STORAGE_API_H
#define G_STORAGE_API_H

#include "core/model/model.h"
#include "core/types.h"
#include "gui/model.h"
#include <functional>
#include <string>
#include <vector>

namespace giada::m
{
class Engine;
class Mixer;
class MidiDispatcher;
class MidiSynchronizer;
class ChannelManager;
class KernelAudio;
class Sequencer;
class ActionRecorder;
class StorageApi
{
public:
	StorageApi(Engine&, model::Model&, PluginManager&, MidiSynchronizer&,
	    Mixer&, ChannelManager&, KernelAudio&, Sequencer&, ActionRecorder&);

	/* storeProject
	Saves the current project. Returns true on success. */

	bool storeProject(const std::string& projectPath, const v::Model&,
	    std::function<void(float)>       progress) const;

	/* loadProject
	Loads a new project. Returns a model::LoadState object containing the
	operation state. */

	model::LoadState loadProject(const std::string& projectPath, std::function<void(float)> progress);

private:
	Engine&           m_engine;
	model::Model&     m_model;
	PluginManager&    m_pluginManager;
	MidiSynchronizer& m_midiSynchronizer;
	Mixer&            m_mixer;
	ChannelManager&   m_channelManager;
	KernelAudio&      m_kernelAudio;
	Sequencer&        m_sequencer;
	ActionRecorder&   m_actionRecorder;
};
} // namespace giada::m

#endif
