/* -----------------------------------------------------------------------------
 *
 * Giada - Your Hardcore Loopmachine
 *
 * -----------------------------------------------------------------------------
 *
 * Copyright (C) 2010-2025 Giovanni <PERSON> | Monocasual Laboratories
 *
 * This file is part of Giada - Your Hardcore Loopmachine.
 *
 * Giada - Your Hardcore Loopmachine is free software: you can
 * redistribute it and/or modify it under the terms of the GNU General
 * Public License as published by the Free Software Foundation, either
 * version 3 of the License, or (at your option) any later version.
 *
 * Giada - Your Hardcore Loopmachine is distributed in the hope that it
 * will be useful, but WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Giada - Your Hardcore Loopmachine. If not, see
 * <http://www.gnu.org/licenses/>.
 *
 * -------------------------------------------------------------------------- */

#include "gui/dialogs/sampleEditor.h"
#include "core/engine.h"
#include "gui/dialogs/warnings.h"
#include "gui/ui.h"

extern giada::v::Ui*     g_ui;
extern giada::m::Engine* g_engine;

namespace giada::c::sampleEditor
{
Data::Data(const m::Channel& c)
: channelId(c.id)
, name(c.name)
, volume(c.volume)
, pan(c.pan)
, pitch(c.sampleChannel->pitch)
, begin(c.sampleChannel->begin)
, end(c.sampleChannel->end)
, shift(c.sampleChannel->shift)
, waveSize(c.sampleChannel->getWave()->getBuffer().countFrames())
, waveBits(c.sampleChannel->getWave()->getBits())
, waveDuration(c.sampleChannel->getWave()->getDuration())
, waveRate(c.sampleChannel->getWave()->getRate())
, wavePath(c.sampleChannel->getWave()->getPath())
, isLogical(c.sampleChannel->getWave()->isLogical())
, m_channel(&c)
{
}

ChannelStatus Data::a_getPreviewStatus() const
{
	return g_engine->getSampleEditorApi().getPreviewStatus();
}

Frame Data::a_getPreviewTracker() const
{
	return g_engine->getSampleEditorApi().getPreviewTracker();
}

const m::Wave& Data::getWaveRef() const
{
	return *m_channel->sampleChannel->getWave();
}

Frame Data::getFramesInBar() const
{
	return g_engine->getMainApi().getFramesInBar();
}

Frame Data::getFramesInLoop() const
{
	return g_engine->getMainApi().getFramesInLoop();
}

/* -------------------------------------------------------------------------- */
/* -------------------------------------------------------------------------- */
/* -------------------------------------------------------------------------- */

Data getData(ID channelId)
{
	return Data(g_engine->getChannelsApi().get(channelId));
}

/* -------------------------------------------------------------------------- */

v::gdSampleEditor* getWindow()
{
	return static_cast<v::gdSampleEditor*>(g_ui->getSubwindow(WID_SAMPLE_EDITOR));
}

/* -------------------------------------------------------------------------- */

void setBeginEnd(ID channelId, Frame b, Frame e)
{
	g_engine->getSampleEditorApi().setBeginEnd(channelId, b, e);
}

/* -------------------------------------------------------------------------- */

void cut(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().cut(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void copy(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().copy(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void paste(ID channelId, Frame a)
{
	g_engine->getSampleEditorApi().paste(channelId, a);
	getWindow()->rebuild();
}

/* -------------------------------------------------------------------------- */

void silence(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().silence(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void fade(ID channelId, Frame a, Frame b, m::wfx::Fade type)
{
	g_engine->getSampleEditorApi().fade(channelId, a, b, type);
}

/* -------------------------------------------------------------------------- */

void smoothEdges(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().smoothEdges(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void reverse(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().reverse(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void normalize(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().normalize(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void trim(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().trim(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void preparePreview(ID channelId)
{
	g_engine->getSampleEditorApi().loadPreviewChannel(channelId);
}

void setLoop(bool shouldLoop)
{
	g_engine->getSampleEditorApi().setPreviewLoop(shouldLoop);
}

void togglePreview()
{
	g_engine->getSampleEditorApi().togglePreview();
}

void setPreviewTracker(Frame f)
{
	g_engine->getSampleEditorApi().setPreviewTracker(f);
	getWindow()->refresh();
}

void cleanupPreview()
{
	g_engine->getSampleEditorApi().freePreviewChannel();
}

/* -------------------------------------------------------------------------- */

void toNewChannel(ID channelId, Frame a, Frame b)
{
	g_engine->getSampleEditorApi().toNewChannel(channelId, a, b);
}

/* -------------------------------------------------------------------------- */

void reload(ID channelId)
{
	if (!v::gdConfirmWin(g_ui->getI18Text(v::LangMap::COMMON_WARNING), "Reload sample: are you sure?"))
		return;
	g_engine->getSampleEditorApi().reload(channelId);
}

/* -------------------------------------------------------------------------- */

void shift(ID channelId, Frame offset)
{
	g_engine->getSampleEditorApi().shift(channelId, offset);
}
} // namespace giada::c::sampleEditor
