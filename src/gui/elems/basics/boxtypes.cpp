/* -----------------------------------------------------------------------------
 *
 * Giada - Your Hardcore Loopmachine
 *
 * boxtypes
 *
 * -----------------------------------------------------------------------------
 *
 * Copyright (C) 2010-2025 Giovanni <PERSON> | Monocasual Laboratories
 *
 * This file is part of Giada - Your Hardcore Loopmachine.
 *
 * Giada - Your Hardcore Loopmachine is free software: you can
 * redistribute it and/or modify it under the terms of the GNU General
 * Public License as published by the Free Software Foundation, either
 * version 3 of the License, or (at your option) any later version.
 *
 * Giada - Your Hardcore Loopmachine is distributed in the hope that it
 * will be useful, but WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 * See the GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Giada - Your Hardcore Loopmachine. If not, see
 * <http://www.gnu.org/licenses/>.
 *
 * -------------------------------------------------------------------------- */

#include "boxtypes.h"
#include "../../../core/const.h"
#include <FL/fl_draw.H>

void g_customBorderBox(int x, int y, int w, int h, Fl_Color c)
{
	fl_color(c);
	fl_rectf(x, y, w, h);
	fl_color(G_COLOR_GREY_4);
	fl_rect(x, y, w, h);
}

void g_customUpBox(int x, int y, int w, int h, Fl_Color /*c*/)
{
	fl_color(G_COLOR_GREY_2);
	fl_rectf(x, y, w, h);
	fl_color(G_COLOR_GREY_2);
	fl_rect(x, y, w, h);
}

void g_customDownBox(int x, int y, int w, int h, Fl_Color c)
{
	fl_color(c);
	fl_rectf(x, y, w, h);
	fl_color(G_COLOR_GREY_2);
	fl_rect(x, y, w, h);
}
